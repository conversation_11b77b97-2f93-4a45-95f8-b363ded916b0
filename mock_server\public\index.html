<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flutter Counter Mock Server</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .counter-display {
            text-align: center;
            margin: 30px 0;
        }
        .counter-value {
            font-size: 48px;
            font-weight: bold;
            color: #2196F3;
            margin: 10px 0;
        }
        .buttons {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #1976D2;
        }
        button.danger {
            background: #f44336;
        }
        button.danger:hover {
            background: #d32f2f;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .history {
            margin-top: 30px;
        }
        .history-item {
            background: #f9f9f9;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        .timestamp {
            color: #666;
            font-size: 12px;
        }
        .api-docs {
            margin-top: 30px;
            background: #f0f0f0;
            padding: 20px;
            border-radius: 5px;
        }
        .endpoint {
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Flutter Counter Mock Server</h1>
        
        <div class="counter-display">
            <div>Counter Value:</div>
            <div class="counter-value" id="counterValue">0</div>
            <div class="timestamp">Last updated: <span id="lastUpdated">-</span></div>
        </div>
        
        <div class="buttons">
            <button onclick="updateCounter('increment')">➕ Increment</button>
            <button onclick="updateCounter('decrement')">➖ Decrement</button>
            <button onclick="resetCounter()" class="danger">🔄 Reset</button>
            <button onclick="refreshData()">🔄 Refresh</button>
        </div>
        
        <div class="info">
            <strong>📱 Flutter App Connection:</strong><br>
            Your Flutter app can connect to this server using the API endpoints below.
            The server stores data in memory and provides real-time updates.
        </div>
        
        <div class="history">
            <h3>📊 Recent History</h3>
            <div id="historyContainer">
                <div class="history-item">No history yet</div>
            </div>
        </div>
        
        <div class="api-docs">
            <h3>🔗 API Endpoints</h3>
            <div><strong>GET</strong> <span class="endpoint">http://localhost:3000/api/counter</span> - Get current counter</div>
            <div><strong>POST</strong> <span class="endpoint">http://localhost:3000/api/counter</span> - Update counter</div>
            <div><strong>GET</strong> <span class="endpoint">http://localhost:3000/api/counter/history</span> - Get history</div>
            <div><strong>POST</strong> <span class="endpoint">http://localhost:3000/api/counter/reset</span> - Reset counter</div>
        </div>
    </div>

    <script>
        // Load initial data
        refreshData();
        
        // Auto-refresh every 2 seconds
        setInterval(refreshData, 2000);
        
        async function refreshData() {
            try {
                const response = await fetch('/api/counter');
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('counterValue').textContent = result.data.count;
                    document.getElementById('lastUpdated').textContent = 
                        new Date(result.data.lastUpdated).toLocaleString();
                    
                    // Update history
                    updateHistory(result.data.history);
                }
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        }
        
        async function updateCounter(action) {
            try {
                const response = await fetch('/api/counter', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ action: action })
                });
                
                const result = await response.json();
                if (result.success) {
                    refreshData();
                }
            } catch (error) {
                console.error('Error updating counter:', error);
            }
        }
        
        async function resetCounter() {
            if (confirm('Are you sure you want to reset the counter to 0?')) {
                try {
                    const response = await fetch('/api/counter/reset', {
                        method: 'POST'
                    });
                    
                    const result = await response.json();
                    if (result.success) {
                        refreshData();
                    }
                } catch (error) {
                    console.error('Error resetting counter:', error);
                }
            }
        }
        
        function updateHistory(history) {
            const container = document.getElementById('historyContainer');
            
            if (!history || history.length === 0) {
                container.innerHTML = '<div class="history-item">No history yet</div>';
                return;
            }
            
            container.innerHTML = history.slice().reverse().map(item => `
                <div class="history-item">
                    <strong>${item.action.toUpperCase()}</strong>: ${item.previousValue} → ${item.newValue}
                    <div class="timestamp">${new Date(item.timestamp).toLocaleString()}</div>
                </div>
            `).join('');
        }
    </script>
</body>
</html>
