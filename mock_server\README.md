# Flutter Counter Mock Server

This is a local mock server for testing your Flutter counter app without Firebase/Firestore.

## Features

- 🚀 REST API endpoints for counter operations
- 🌐 Web interface to view and manage data
- 📊 History tracking of all operations
- 🔄 Real-time updates
- 💾 In-memory data storage

## Setup Instructions

### 1. Install Node.js
Make sure you have Node.js installed on your system.

### 2. Install Dependencies
```bash
cd mock_server
npm install
```

### 3. Start the Server
```bash
npm start
```

The server will start at: http://localhost:3000

## API Endpoints

- **GET** `/api/counter` - Get current counter value
- **POST** `/api/counter` - Update counter (increment/decrement)
- **GET** `/api/counter/history` - Get operation history
- **POST** `/api/counter/reset` - Reset counter to 0

## Web Interface

Visit http://localhost:3000 in your browser to see the web interface where you can:
- View current counter value
- Increment/Decrement the counter
- Reset the counter
- View operation history
- Monitor real-time updates

## Flutter App Integration

### Option 1: Use Mock Server (Recommended for Testing)
```bash
flutter run -t lib/main_mock.dart -d chrome
```

### Option 2: Use Original Firestore Version
```bash
flutter run -t lib/main.dart -d chrome
```

## API Usage Examples

### Get Counter Value
```bash
curl http://localhost:3000/api/counter
```

### Increment Counter
```bash
curl -X POST http://localhost:3000/api/counter \
  -H "Content-Type: application/json" \
  -d '{"action": "increment"}'
```

### Decrement Counter
```bash
curl -X POST http://localhost:3000/api/counter \
  -H "Content-Type: application/json" \
  -d '{"action": "decrement"}'
```

### Reset Counter
```bash
curl -X POST http://localhost:3000/api/counter/reset
```

## Data Format

The server returns data in this format:
```json
{
  "success": true,
  "data": {
    "count": 5,
    "lastUpdated": "2024-01-15T10:30:00.000Z",
    "history": [
      {
        "previousValue": 4,
        "newValue": 5,
        "action": "increment",
        "timestamp": "2024-01-15T10:30:00.000Z"
      }
    ]
  }
}
```

## Development

To run the server in development mode with auto-restart:
```bash
npm run dev
```

## Notes

- Data is stored in memory and will be lost when the server restarts
- The server automatically enables CORS for Flutter web development
- History is limited to the last 10 operations
- The web interface auto-refreshes every 2 seconds
