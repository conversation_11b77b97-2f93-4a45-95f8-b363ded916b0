import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';

class CounterAppMock extends StatefulWidget {
  @override
  State<CounterAppMock> createState() => _CounterAppMockState();
}

class _CounterAppMockState extends State<CounterAppMock> {
  int _counter = 0;
  String _lastUpdated = '';
  bool _isLoading = false;
  Timer? _refreshTimer;
  
  // Mock server URL
  static const String baseUrl = 'http://localhost:3000/api';

  @override
  void initState() {
    super.initState();
    _fetchCounterValue();
    // Auto-refresh every 2 seconds to simulate real-time updates
    _refreshTimer = Timer.periodic(Duration(seconds: 2), (timer) {
      _fetchCounterValue();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  Future<void> _fetchCounterValue() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/counter'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          setState(() {
            _counter = data['data']['count'];
            _lastUpdated = data['data']['lastUpdated'];
          });
        }
      } else {
        print('Failed to fetch counter: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching counter: $e');
      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to connect to server. Make sure the mock server is running.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateCounter(String action) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/counter'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'action': action}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          setState(() {
            _counter = data['data']['count'];
            _lastUpdated = data['data']['lastUpdated'];
          });
        }
      } else {
        print('Failed to update counter: ${response.statusCode}');
      }
    } catch (e) {
      print('Error updating counter: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update counter. Check server connection.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _resetCounter() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await http.post(
        Uri.parse('$baseUrl/counter/reset'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          setState(() {
            _counter = data['data']['count'];
            _lastUpdated = data['data']['lastUpdated'];
          });
        }
      }
    } catch (e) {
      print('Error resetting counter: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatDateTime(String isoString) {
    if (isoString.isEmpty) return '';
    try {
      final dateTime = DateTime.parse(isoString);
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}:${dateTime.second.toString().padLeft(2, '0')}';
    } catch (e) {
      return '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Counter App (Mock Server)'),
        backgroundColor: Colors.green,
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: _fetchCounterValue,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.cloud_off,
              size: 48,
              color: Colors.green,
            ),
            SizedBox(height: 10),
            Text(
              'Mock Server Mode',
              style: TextStyle(
                fontSize: 16,
                color: Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 30),
            Text(
              'Counter Value:',
              style: TextStyle(fontSize: 24),
            ),
            SizedBox(height: 10),
            Text(
              '$_counter',
              style: TextStyle(fontSize: 48, fontWeight: FontWeight.bold),
            ),
            if (_lastUpdated.isNotEmpty) ...[
              SizedBox(height: 10),
              Text(
                'Last updated: ${_formatDateTime(_lastUpdated)}',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
            SizedBox(height: 30),
            if (_isLoading)
              CircularProgressIndicator()
            else
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton(
                    onPressed: () => _updateCounter('increment'),
                    child: Text('Increment'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  SizedBox(width: 20),
                  ElevatedButton(
                    onPressed: () => _updateCounter('decrement'),
                    child: Text('Decrement'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _resetCounter,
              child: Text('Reset'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
            ),
            SizedBox(height: 40),
            Container(
              padding: EdgeInsets.all(16),
              margin: EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                children: [
                  Text(
                    '🌐 Server Info',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Connected to: http://localhost:3000',
                    style: TextStyle(fontSize: 12),
                  ),
                  Text(
                    'Web interface: http://localhost:3000',
                    style: TextStyle(fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
