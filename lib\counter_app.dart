import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class CounterApp extends StatefulWidget {
  @override
  State<CounterApp> createState() => _CounterAppState();
}

class _CounterAppState extends State<CounterApp> {
  int _counter = 0;
  final _firestore = FirebaseFirestore.instance;

  @override
  void initState() {
    super.initState();
    _firestore.collection('counter').doc('value').snapshots().listen((snapshot) {
      if (snapshot.exists) {
        setState(() {
          _counter = snapshot['count'];
        });
      }
    });
  }

  void _updateCounter(int delta) {
    setState(() {
      _counter += delta;
    });
    _firestore.collection('counter').doc('value').set({'count': _counter});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Counter App')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Counter Value:',
              style: TextStyle(fontSize: 24),
            ),
            Text(
              '$_counter',
              style: TextStyle(fontSize: 48, fontWeight: FontWeight.bold),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ElevatedButton(
                  onPressed: () => _updateCounter(1),
                  child: Text('Increment'),
                ),
                SizedBox(width: 20),
                ElevatedButton(
                  onPressed: () => _updateCounter(-1),
                  child: Text('Decrement'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
