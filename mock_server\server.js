const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');

const app = express();
const PORT = 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(express.static('public'));

// In-memory data storage (simulating database)
let counterData = {
  count: 0,
  lastUpdated: new Date().toISOString(),
  history: []
};

// API Routes

// GET /api/counter - Get current counter value
app.get('/api/counter', (req, res) => {
  console.log('GET /api/counter - Current value:', counterData.count);
  res.json({
    success: true,
    data: counterData
  });
});

// POST /api/counter - Update counter value
app.post('/api/counter', (req, res) => {
  const { action, value } = req.body;
  
  let newCount = counterData.count;
  
  if (action === 'increment') {
    newCount += (value || 1);
  } else if (action === 'decrement') {
    newCount -= (value || 1);
  } else if (action === 'set') {
    newCount = value || 0;
  }
  
  // Add to history
  counterData.history.push({
    previousValue: counterData.count,
    newValue: newCount,
    action: action,
    timestamp: new Date().toISOString()
  });
  
  // Keep only last 10 history entries
  if (counterData.history.length > 10) {
    counterData.history = counterData.history.slice(-10);
  }
  
  counterData.count = newCount;
  counterData.lastUpdated = new Date().toISOString();
  
  console.log(`POST /api/counter - Action: ${action}, New value: ${newCount}`);
  
  res.json({
    success: true,
    data: counterData
  });
});

// GET /api/counter/history - Get counter history
app.get('/api/counter/history', (req, res) => {
  res.json({
    success: true,
    data: counterData.history
  });
});

// POST /api/counter/reset - Reset counter to 0
app.post('/api/counter/reset', (req, res) => {
  counterData.history.push({
    previousValue: counterData.count,
    newValue: 0,
    action: 'reset',
    timestamp: new Date().toISOString()
  });
  
  counterData.count = 0;
  counterData.lastUpdated = new Date().toISOString();
  
  console.log('POST /api/counter/reset - Counter reset to 0');
  
  res.json({
    success: true,
    data: counterData
  });
});

// Serve the web interface
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Mock server running at http://localhost:${PORT}`);
  console.log(`📊 API endpoints:`);
  console.log(`   GET  http://localhost:${PORT}/api/counter`);
  console.log(`   POST http://localhost:${PORT}/api/counter`);
  console.log(`   GET  http://localhost:${PORT}/api/counter/history`);
  console.log(`   POST http://localhost:${PORT}/api/counter/reset`);
  console.log(`🌐 Web interface: http://localhost:${PORT}`);
});
